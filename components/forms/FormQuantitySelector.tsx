import React from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

interface FormQuantitySelectorProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
  min?: number;
  max?: number;
}

export function FormQuantitySelector<T extends FieldValues>({
  name,
  control,
  label,
  containerStyle,
  rules,
  min = 1,
  max = 999,
}: FormQuantitySelectorProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  const handleDecrease = () => {
    const currentValue = Number(field.value) || min;
    if (currentValue > min) {
      field.onChange(currentValue - 1);
    }
  };

  const handleIncrease = () => {
    const currentValue = Number(field.value) || min;
    if (currentValue < max) {
      field.onChange(currentValue + 1);
    }
  };

  const currentValue = Number(field.value) || min;
  const isMinReached = currentValue <= min;
  const isMaxReached = currentValue >= max;

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={styles.selectorContainer}>
        <TouchableOpacity
          style={[styles.button, isMinReached && styles.buttonDisabled]}
          onPress={handleDecrease}
          disabled={isMinReached}
          activeOpacity={0.7}
        >
          <Ionicons
            name="remove"
            size={16}
            color={isMinReached ? Colors.textLight : Colors.white}
          />
        </TouchableOpacity>

        <Text style={styles.quantityText}>{currentValue}</Text>

        <TouchableOpacity
          style={[styles.button, isMaxReached && styles.buttonDisabled]}
          onPress={handleIncrease}
          disabled={isMaxReached}
          activeOpacity={0.7}
        >
          <Ionicons
            name="add"
            size={16}
            color={isMaxReached ? Colors.textLight : Colors.white}
          />
        </TouchableOpacity>
      </View>
      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 8,
    textTransform: "capitalize",
  },
  selectorContainer: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-start",
    backgroundColor: Colors.white,
    borderRadius: 8,
    overflow: "hidden",
  },
  button: {
    width: 44,
    height: 44,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.primary,
    borderRadius: 8,
  },
  buttonDisabled: {
    backgroundColor: Colors.lightGray,
  },
  quantityText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
    marginHorizontal: 20,
    minWidth: 30,
    textAlign: "center",
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
});
