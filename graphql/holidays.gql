query Holidays {
  holidays {
    id
    name
    date
    description
  }
}

mutation CreateHoliday($createHolidayInput: CreateHolidayInput!) {
  createHoliday(createHolidayInput: $createHolidayInput) {
    id
  }
}

mutation DeleteHoliday($holidayId: String!) {
  removeHoliday(id: $holidayId) {
    id
  }
}

mutation UpdateHoliday($updateHolidayInput: UpdateHolidayInput!, $holidayId: String!) {
  updateHoliday(id: $holidayId, updateHolidayInput: $updateHolidayInput) {
    id
  }
}
