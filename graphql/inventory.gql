mutation CreateInventoryRequest($input: CreateInventoryRequestInput!) {
  createInventoryRequest(input: $input) {
    _id
  }
}

query GetInventory {
  inventory {
    _id
    id
    createdAt
    updatedAt
    item
    description
    type
    items {
      item
      quantity
      sku
      costPrice
      sellingPrice
    }
    attributes {
      attibuteName
      attributeValues
    }
  }
}

query InventoryRequestHistory($input: FindInventoryRequestsInput!) {
  inventoryRequests(filter: $input) {
    _id
    id
    createdAt
    updatedAt
    requestedBy {
      fullname
    }
    items {
      item
      sku
      quantity
      costPrice
      sellingPrice
      selectedAttributes {
        attributeName
        value
      }
    }
    acceptedAt
    status
    inventoryType
    inventory {
      item
    }
  }
}

query InventoryRequestDetails($id: String!) {
  inventoryRequest(id: $id) {
    _id
    id
    createdAt
    updatedAt
    requestedBy {
      fullname
      id
    }
    items {
      item
      sku
      quantity
      costPrice
      sellingPrice
      selectedAttributes {
        attributeName
        value
      }
    }
    acceptedAt
    status
    inventoryType
    inventory {
      item
      description
      type
    }
  }
}
