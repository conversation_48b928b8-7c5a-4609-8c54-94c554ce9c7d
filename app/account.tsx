import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  Platform,
  Pressable,
} from "react-native";
import { useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { useSession } from "@/providers/auth-provider";
import { useMeQuery } from "@/generated/graphql";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
} from "react-native-reanimated";

// Define types for the AccountOption component
interface AccountOptionProps {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  onPress: () => void;
  disabled?: boolean;
  index: number;
}

// Account option item component
function AccountOption({
  icon,
  label,
  onPress,
  disabled = false,
  index,
}: AccountOptionProps) {
  // Animation values
  const translateX = useSharedValue(80); // Start from right (100px off-screen)
  const opacity = useSharedValue(0);
  const pressScale = useSharedValue(1);
  const pressOpacity = useSharedValue(1);

  // Animated style
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }, { scale: pressScale.value }],
    opacity: opacity.value * pressOpacity.value,
  }));

  // Start animation when component mounts
  useEffect(() => {
    // Stagger animations with delay based on index
    const delay = index * 50; // 50ms delay between each item

    translateX.value = withDelay(delay, withTiming(0, { duration: 200 }));
    opacity.value = withDelay(delay, withTiming(1, { duration: 50 }));
  }, [index]);

  // Press animation handlers
  const handlePressIn = () => {
    if (Platform.OS === "ios") {
      // iOS: Subtle opacity change
      pressOpacity.value = withTiming(0.6, { duration: 150 });
    } else {
      // Android: Subtle scale effect (ripple is handled by Pressable)
      pressScale.value = withTiming(0.98, { duration: 150 });
    }
  };

  const handlePressOut = () => {
    if (Platform.OS === "ios") {
      // iOS: Restore opacity
      pressOpacity.value = withTiming(1, { duration: 150 });
    } else {
      // Android: Restore scale
      pressScale.value = withTiming(1, { duration: 150 });
    }
  };

  const handlePress = () => {
    // Call the original onPress after a slight delay for animation
    setTimeout(() => {
      onPress();
    }, 50);
  };

  return (
    <Animated.View style={animatedStyle}>
      <Pressable
        style={[styles.optionItem, disabled && styles.optionItemDisabled]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        android_ripple={{
          color: `${Colors.primary}20`,
          borderless: false,
          radius: 200,
        }}
      >
        <View
          style={[
            styles.optionIconContainer,
            disabled && styles.optionIconContainerDisabled,
          ]}
        >
          <Ionicons
            name={icon}
            size={22}
            color={disabled ? Colors.textSecondary : Colors.primary}
          />
        </View>
        <Text
          style={[styles.optionLabel, disabled && styles.optionLabelDisabled]}
        >
          {label}
        </Text>
        <Ionicons
          name="chevron-forward"
          size={20}
          color={disabled ? Colors.textSecondary : Colors.textLight}
        />
      </Pressable>
    </Animated.View>
  );
}

export default function AccountScreen() {
  const router = useRouter();
  const { signOut } = useSession();

  // Get user data to check face registration status
  const { data: meData } = useMeQuery();

  // Check if user has face registration
  const hasFaceRegistration = meData?.me?.faceInformation?.faceId;

  // Handle option press
  const handleOptionPress = (optionName: string) => {
    // Navigate based on the option selected
    if (optionName === "Incident Reporting") {
      router.push("/incident-reporting" as any);
    } else if (optionName === "Personal Information") {
      router.push("/personal-profile" as any);
    } else if (optionName === "Uniform Request") {
      router.push("/uniform-request" as any);
    } else if (optionName === "Inventory History") {
      router.push("/inventory-history" as any);
    } else if (optionName === "Claims") {
      router.push("claims" as any);
    } else if (optionName === "Claims History") {
      router.push("claims/claims-list" as any);
    } else if (optionName === "Face Registration") {
      router.push("/face-registration" as any);
    } else if (optionName === "Sign Out") {
      Alert.alert("Sign Out", "Are you sure you want to sign out?", [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Sign Out",
          onPress: () => {
            signOut();
            router.replace("/login");
          },
          style: "destructive",
        },
      ]);
    }
    // Add more navigation options as needed
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* User account section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>User account</Text>
        <View style={styles.optionsList}>
          <AccountOption
            icon="person-outline"
            label="Personal Information"
            onPress={() => handleOptionPress("Personal Information")}
            index={0}
          />
          <AccountOption
            icon="scan"
            label={
              hasFaceRegistration ? "Face ID Registered" : "Face Registration"
            }
            onPress={() => handleOptionPress("Face Registration")}
            disabled={!!hasFaceRegistration}
            index={1}
          />
          <AccountOption
            icon="warning-outline"
            label="Incident Reporting"
            onPress={() => handleOptionPress("Incident Reporting")}
            index={2}
          />
          <AccountOption
            icon="shirt-outline"
            label="Uniform Request"
            onPress={() => handleOptionPress("Uniform Request")}
            index={3}
          />
          <AccountOption
            icon="archive-outline"
            label="Inventory History"
            onPress={() => handleOptionPress("Inventory History")}
            index={4}
          />
          <AccountOption
            icon="cash-outline"
            label="Claims"
            onPress={() => handleOptionPress("Claims")}
            index={5}
          />
          <AccountOption
            icon="cash-outline"
            label="Claims History"
            onPress={() => handleOptionPress("Claims History")}
            index={6}
          />

          <AccountOption
            icon="help-circle-outline"
            label="Help & Support"
            onPress={() => handleOptionPress("Help & Support")}
            index={7}
          />
          <AccountOption
            icon="log-out-outline"
            label="Sign Out"
            onPress={() => handleOptionPress("Sign Out")}
            index={8}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  optionsList: {
    marginHorizontal: 16,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginBottom: 8,
    backgroundColor: Colors.white,
    borderRadius: 12,
  },
  optionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  optionLabel: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  optionItemDisabled: {
    opacity: 0.5,
  },
  optionIconContainerDisabled: {
    backgroundColor: `${Colors.textSecondary}15`,
  },
  optionLabelDisabled: {
    color: Colors.textSecondary,
  },
});
