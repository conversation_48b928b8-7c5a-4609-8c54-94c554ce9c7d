import React, { useMemo } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useRouter, useLocalSearchParams } from "expo-router";
// import { useHolidaysQuery } from "@/generated/graphql"; // Will be enabled after GraphQL regeneration
import { format, startOfWeek, endOfWeek } from "date-fns";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
} from "react-native-reanimated";

export default function HolidaysScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();

  // Get week parameters if passed from calendar
  const weekStart = params.weekStart
    ? new Date(params.weekStart as string)
    : null;
  const weekEnd = params.weekEnd ? new Date(params.weekEnd as string) : null;

  // Animation values
  const headerOpacity = useSharedValue(0);
  const contentTranslateY = useSharedValue(20);

  React.useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 400 });
    contentTranslateY.value = withSpring(0, { damping: 15, stiffness: 100 });
  }, []);

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: contentTranslateY.value }],
  }));

  // Mock holidays data (will be replaced when GraphQL is regenerated)
  const isLoading = false;
  const error = null;
  const holidaysData = {
    holidays: [
      {
        id: "1",
        name: "New Year's Day",
        date: new Date("2024-01-01"),
        description: "New Year celebration",
      },
      {
        id: "2",
        name: "Independence Day",
        date: new Date("2024-07-04"),
        description: "Independence Day celebration",
      },
      {
        id: "3",
        name: "Christmas Day",
        date: new Date("2024-12-25"),
        description: "Christmas celebration",
      },
    ],
  };

  // Filter holidays based on week if provided
  const filteredHolidays = useMemo(() => {
    if (!holidaysData?.holidays) return [];

    if (weekStart && weekEnd) {
      return holidaysData.holidays.filter((holiday: any) => {
        const holidayDate = new Date(holiday.date);
        return holidayDate >= weekStart && holidayDate <= weekEnd;
      });
    }

    return holidaysData.holidays;
  }, [holidaysData, weekStart, weekEnd]);

  const handleBackPress = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={28} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {weekStart && weekEnd ? "Weekly Holidays" : "Holidays"}
        </Text>
        <View style={styles.headerAccent} />
      </Animated.View>

      {/* Week Info */}
      {weekStart && weekEnd && (
        <View style={styles.weekInfo}>
          <Text style={styles.weekInfoText}>
            {format(weekStart, "MMM dd")} - {format(weekEnd, "MMM dd, yyyy")}
          </Text>
        </View>
      )}

      <Animated.View style={[styles.content, contentAnimatedStyle]}>
        {/* Loading State */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading holidays...</Text>
          </View>
        )}

        {/* Error State */}
        {error && (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={48} color={Colors.error} />
            <Text style={styles.errorText}>Failed to load holidays</Text>
          </View>
        )}

        {/* Holidays List */}
        {!isLoading && !error && (
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {filteredHolidays.length > 0 ? (
              filteredHolidays.map((holiday: any, index: number) => (
                <View key={holiday.id} style={styles.holidayCard}>
                  <View style={styles.holidayIcon}>
                    <Ionicons name="sunny" size={24} color={Colors.primary} />
                  </View>
                  <View style={styles.holidayInfo}>
                    <Text style={styles.holidayName}>{holiday.name}</Text>
                    <Text style={styles.holidayDate}>
                      {format(new Date(holiday.date), "EEEE, MMMM dd, yyyy")}
                    </Text>
                    {holiday.description && (
                      <Text style={styles.holidayDescription}>
                        {holiday.description}
                      </Text>
                    )}
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons
                  name="calendar-outline"
                  size={64}
                  color={Colors.textLight}
                />
                <Text style={styles.emptyTitle}>
                  {weekStart && weekEnd
                    ? "No holidays this week"
                    : "No holidays found"}
                </Text>
                <Text style={styles.emptySubtitle}>
                  {weekStart && weekEnd
                    ? "There are no holidays scheduled for this week."
                    : "There are no holidays in the system."}
                </Text>
              </View>
            )}
          </ScrollView>
        )}
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
    textAlign: "center",
    marginHorizontal: 16,
  },
  headerAccent: {
    width: 36,
  },
  weekInfo: {
    backgroundColor: Colors.white,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  weekInfoText: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    textAlign: "center",
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: "center",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    gap: 16,
  },
  holidayCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    flexDirection: "row",
    alignItems: "flex-start",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  holidayIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  holidayInfo: {
    flex: 1,
  },
  holidayName: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 4,
  },
  holidayDate: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: "500",
    marginBottom: 8,
  },
  holidayDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
  },
});
