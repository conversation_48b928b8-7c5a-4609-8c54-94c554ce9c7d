import React, { useState, useRef } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Animated,
  Easing,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { StatusBar } from "expo-status-bar";
import { useRouter } from "expo-router";
import * as DocumentPicker from "expo-document-picker";
import * as Linking from "expo-linking";
import { format } from "date-fns";
import {
  useUserDocumentsQuery,
  useCreateUserDocumentMutation,
  useDeleteUserDocumentMutation,
  useCreateSignedUploadUrlMutation,
  EvidenceType,
} from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";

// Supported file types for document upload
const SUPPORTED_FILE_TYPES = [
  "application/pdf",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
  "application/vnd.ms-excel", // .xls
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/msword", // .doc
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/gif",
  "application/zip",
  "text/plain",
  "text/csv",
];

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Get file icon and color based on file extension
const getFileIconInfo = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (extension) {
    case "pdf":
      return { icon: "document-text", color: "#E94335" };
    case "docx":
    case "doc":
      return { icon: "document-text", color: "#4285F4" };
    case "xlsx":
    case "xls":
    case "csv":
      return { icon: "grid", color: "#0F9D58" };
    case "zip":
    case "rar":
    case "7z":
      return { icon: "archive", color: "#F4B400" };
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
    case "bmp":
    case "webp":
      return { icon: "image", color: "#9C27B0" };
    case "txt":
    case "rtf":
      return { icon: "document-text", color: "#607D8B" };
    case "mp4":
    case "avi":
    case "mov":
    case "wmv":
      return { icon: "videocam", color: "#FF5722" };
    case "mp3":
    case "wav":
    case "aac":
      return { icon: "musical-notes", color: "#FF9800" };
    default:
      return { icon: "document", color: "#FF5722" };
  }
};

interface DocumentItemProps {
  id: string;
  name: string;
  url: string;
  createdAt: string;
  onPress: () => void;
  onDelete: () => void;
  index?: number;
}

/**
 * Document item component with animations
 */
function DocumentItem({
  id,
  name,
  url,
  createdAt,
  onPress,
  onDelete,
  index = 0,
}: DocumentItemProps) {
  const { icon, color } = getFileIconInfo(name);
  const slideAnim = useRef(new Animated.Value(-50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  // Start entrance animation
  React.useEffect(() => {
    const delay = index * 50; // Stagger the animation

    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        delay,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        delay,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={{
        opacity: opacityAnim,
        transform: [{ translateX: slideAnim }],
      }}
    >
      <TouchableOpacity
        style={styles.documentItem}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View
          style={[styles.fileIconContainer, { backgroundColor: `${color}20` }]}
        >
          <Ionicons name={icon as any} size={24} color={color} />
        </View>

        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {name}
          </Text>
          <View style={styles.fileDetails}>
            <Text style={styles.fileDate}>{createdAt.toString()}</Text>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function DocumentManagementScreen() {
  const router = useRouter();
  const { session } = useSession();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fabAnim = useRef(new Animated.Value(0)).current;

  // GraphQL hooks
  const { data, isLoading, refetch, isRefetching } = useUserDocumentsQuery(
    {
      userDocumentFilterInput: {
        user: session?.userId,
      },
    },
    {
      enabled: !!session?.userId,
    }
  );

  const { mutateAsync: createUserDocument } = useCreateUserDocumentMutation();
  const { mutateAsync: deleteUserDocument } = useDeleteUserDocumentMutation();
  const { mutateAsync: getSignedUrl } = useCreateSignedUploadUrlMutation();

  // Animate FAB entrance
  React.useEffect(() => {
    Animated.spring(fabAnim, {
      toValue: 1,
      tension: 50,
      friction: 7,
      delay: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  // Filter documents based on search query
  const filteredDocuments = React.useMemo(() => {
    if (!data?.userDocuments) return [];

    if (!searchQuery.trim()) {
      return data.userDocuments;
    }

    return data.userDocuments.filter((doc) =>
      doc.documentName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data?.userDocuments, searchQuery]);

  // Upload file to S3 using signed URL
  const uploadFileToS3 = async (
    fileUri: string,
    fileName: string,
    mimeType: string
  ): Promise<string> => {
    try {
      // Get signed upload URL
      const signedUrlData = await getSignedUrl({
        input: {
          key: `user-documents/${session?.userId}/${Date.now()}-${fileName}`,
          contentType: mimeType,
        },
      });

      const { url, fields } = signedUrlData.createSignedUploadUrl;

      // Create form data for S3 upload
      const formData = new FormData();
      Object.entries(fields).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Add the file
      formData.append("file", {
        uri: fileUri,
        type: mimeType,
        name: fileName,
      } as any);

      // Upload to S3
      const uploadResponse = await fetch(url, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload file to S3");
      }

      // Return the S3 URL
      return `${url}/${fields.key}`;
    } catch (error) {
      console.error("Upload failed:", error);
      throw error;
    }
  };

  // Handle document upload
  const handleDocumentUpload = async () => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const result = await DocumentPicker.getDocumentAsync({
        type: SUPPORTED_FILE_TYPES,
        copyToCacheDirectory: true,
        multiple: false,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const fileName = asset.name;
        const mimeType = asset.mimeType || "application/octet-stream";

        // Validate file type
        if (!SUPPORTED_FILE_TYPES.includes(mimeType)) {
          Alert.alert(
            "Unsupported File Type",
            "Please select a supported file type (PDF, Excel, Word, Images, etc.)"
          );
          return;
        }

        setUploadProgress(25);

        // Upload to S3
        const s3Url = await uploadFileToS3(asset.uri, fileName, mimeType);
        setUploadProgress(75);

        // Create document record
        await createUserDocument({
          input: {
            user: session?.userId!,
            documentName: fileName,
            url: s3Url,
          },
        });

        setUploadProgress(100);

        // Refresh the document list
        await refetch();

        Alert.alert("Success", "Document uploaded successfully!");
      }
    } catch (error) {
      console.error("Failed to upload document:", error);
      Alert.alert("Error", "Failed to upload document. Please try again.");
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle document press (open/download)
  const handleDocumentPress = async (document: any) => {
    try {
      const supported = await Linking.canOpenURL(document.url);
      if (supported) {
        await Linking.openURL(document.url);
      } else {
        Alert.alert("Error", "Cannot open this document type");
      }
    } catch (error) {
      console.error("Failed to open document:", error);
      Alert.alert("Error", "Failed to open document");
    }
  };

  // Handle document deletion
  const handleDocumentDelete = async (
    documentId: string,
    documentName: string
  ) => {
    Alert.alert(
      "Delete Document",
      `Are you sure you want to delete "${documentName}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteUserDocument({ documentId });
              await refetch();
              Alert.alert("Success", "Document deleted successfully!");
            } catch (error) {
              console.error("Failed to delete document:", error);
              Alert.alert("Error", "Failed to delete document");
            }
          },
        },
      ]
    );
  };

  const handleBackPress = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading documents...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My Documents</Text>
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => {
            // TODO: Implement search functionality
          }}
        >
          <Ionicons name="search-outline" size={24} color={Colors.white} />
        </TouchableOpacity>
      </View>

      {/* Upload Progress */}
      {isUploading && (
        <View style={styles.uploadProgress}>
          <Text style={styles.uploadText}>Uploading... {uploadProgress}%</Text>
          <View style={styles.progressBar}>
            <View
              style={[styles.progressFill, { width: `${uploadProgress}%` }]}
            />
          </View>
        </View>
      )}

      {/* Content */}
      <View style={styles.content}>
        {filteredDocuments.length > 0 ? (
          <FlatList
            data={filteredDocuments}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isRefetching}
                onRefresh={refetch}
                colors={[Colors.primary]}
              />
            }
            renderItem={({ item, index }) => (
              <DocumentItem
                id={item.id}
                name={item.documentName}
                url={item.url}
                createdAt={format(
                  new Date(item.createdAt),
                  "dd MMM yyyy hh:mm a"
                )}
                onPress={() => handleDocumentPress(item)}
                onDelete={() =>
                  handleDocumentDelete(item.id, item.documentName)
                }
                index={index}
              />
            )}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="document-outline"
              size={64}
              color={Colors.textLight}
            />
            <Text style={styles.emptyText}>
              {searchQuery
                ? "No documents match your search"
                : "No documents uploaded yet"}
            </Text>
            {!searchQuery && (
              <Text style={styles.emptySubtext}>
                Tap the + button to upload your first document
              </Text>
            )}
          </View>
        )}
      </View>

      {/* Animated FAB */}
      <Animated.View
        style={[
          styles.fab,
          {
            transform: [
              { scale: fabAnim },
              {
                rotate: fabAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ["135deg", "0deg"],
                }),
              },
            ],
          },
        ]}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={handleDocumentUpload}
          style={styles.fabButton}
          disabled={isUploading}
        >
          {isUploading ? (
            <ActivityIndicator size="small" color={Colors.white} />
          ) : (
            <Ionicons name="add" size={24} color={Colors.white} />
          )}
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centerContent: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.textSecondary,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
    textAlign: "center",
  },
  searchButton: {
    padding: 4,
  },
  uploadProgress: {
    backgroundColor: Colors.white,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  uploadText: {
    fontSize: 14,
    color: Colors.text,
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: "rgba(0,0,0,0.1)",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: Colors.primary,
  },
  content: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  documentItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  fileIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 4,
  },
  fileDetails: {
    flexDirection: "row",
    alignItems: "center",
  },
  fileSize: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  fileDot: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginHorizontal: 4,
  },
  fileDate: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  moreButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
    textAlign: "center",
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textLight,
    marginTop: 8,
    textAlign: "center",
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: "center",
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  fabButton: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
});
