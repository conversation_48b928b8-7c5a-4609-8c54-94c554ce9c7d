import React, { useState, useEffect, useMemo } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useRouter } from "expo-router";
import { useSession } from "@/providers/auth-provider";
import {
  useGetUserAttendanceQuery,
  useGetShiftsByUserQuery,
} from "@/generated/graphql";
import { startOfWeek, endOfWeek } from "date-fns";

// Days of the week
const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

export default function CalendarScreen() {
  const router = useRouter();
  const { session } = useSession();
  const [selectedDateIndex, setSelectedDateIndex] = useState(0); // Index of the selected date in the week
  const [currentWeekOffset, setCurrentWeekOffset] = useState(0); // 0 = current week, -1 = last week, 1 = next week
  const [weekDates, setWeekDates] = useState<Date[]>([]);

  // Calculate week start and end dates for data fetching
  const weekStartDate = useMemo(() => {
    if (weekDates.length === 0) return new Date();
    return startOfWeek(weekDates[0], { weekStartsOn: 0 }); // Sunday start
  }, [weekDates]);

  const weekEndDate = useMemo(() => {
    if (weekDates.length === 0) return new Date();
    return endOfWeek(weekDates[6], { weekStartsOn: 0 }); // Sunday start
  }, [weekDates]);

  // Fetch attendance data for the selected week
  const { data: attendanceData, isLoading: attendanceLoading } =
    useGetUserAttendanceQuery(
      {
        filter: {
          dateRange: {
            startDate: weekStartDate,
            endDate: weekEndDate,
          },
          userIds: session ? [session.userId] : [],
        },
      },
      {
        enabled: !!session && weekDates.length > 0,
        initialData: { myAttendances: [] },
      }
    );

  // Fetch shifts data for the selected week
  const { data: shiftsData, isLoading: shiftsLoading } =
    useGetShiftsByUserQuery(
      {
        shiftsInput: {
          startDateTime: weekStartDate,
          endDateTime: weekEndDate,
          userId: session?.userId,
        },
      },
      {
        enabled: !!session && weekDates.length > 0,
        initialData: { getUserShifts: [] },
      }
    );

  // Mock holidays data (will be replaced when GraphQL is regenerated)
  const holidaysLoading = false;
  const weekHolidays = useMemo(() => {
    // Mock holidays for demonstration
    const mockHolidays = [
      {
        id: "1",
        name: "New Year's Day",
        date: new Date("2024-01-01"),
        description: "New Year celebration",
      },
      {
        id: "2",
        name: "Christmas Day",
        date: new Date("2024-12-25"),
        description: "Christmas celebration",
      },
    ];

    if (weekDates.length === 0) return [];

    return mockHolidays.filter((holiday) => {
      const holidayDate = new Date(holiday.date);
      return holidayDate >= weekStartDate && holidayDate <= weekEndDate;
    });
  }, [weekStartDate, weekEndDate, weekDates]);

  // Generate dates for the current week
  useEffect(() => {
    const today = new Date();
    const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate the start of the week (Sunday)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay + 7 * currentWeekOffset);

    // Generate an array of dates for the week
    const dates: Date[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date);
    }

    setWeekDates(dates);

    // If we're on the current week (offset = 0), select today
    if (currentWeekOffset === 0) {
      setSelectedDateIndex(currentDay);
    } else {
      setSelectedDateIndex(0);
    }
  }, [currentWeekOffset]);

  // Navigate to previous week
  const goToPreviousWeek = () => {
    setCurrentWeekOffset(currentWeekOffset - 1);
  };

  // Navigate to next week
  const goToNextWeek = () => {
    setCurrentWeekOffset(currentWeekOffset + 1);
  };

  // Format date as a number (1-31)
  const formatDateNumber = (date: Date) => {
    return date.getDate();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Month Display */}
      {weekDates[selectedDateIndex] && (
        <View style={styles.monthHeader}>
          <Text style={styles.monthText}>
            {weekDates[selectedDateIndex].toLocaleString("default", {
              month: "long",
              year: "numeric",
            })}
          </Text>
        </View>
      )}

      {/* Calendar Week View */}
      <View style={styles.calendarContainer}>
        <View style={styles.weekContainer}>
          <TouchableOpacity onPress={goToPreviousWeek} style={styles.navButton}>
            <Ionicons name="chevron-back" size={18} color={Colors.primary} />
          </TouchableOpacity>

          {daysOfWeek.map((day, index) => (
            <Text key={index} style={styles.weekDayText}>
              {day}
            </Text>
          ))}

          <TouchableOpacity onPress={goToNextWeek} style={styles.navButton}>
            <Ionicons name="chevron-forward" size={18} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.datesContainer}>
          <View style={styles.navButtonPlaceholder} />

          <View style={styles.datesRow}>
            {weekDates.map((date, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dateCircle,
                  selectedDateIndex === index
                    ? styles.selectedDateCircle
                    : null,
                ]}
                onPress={() => {
                  setSelectedDateIndex(index);
                }}
              >
                <Text
                  style={[
                    styles.dateText,
                    selectedDateIndex === index
                      ? styles.selectedDateText
                      : null,
                  ]}
                >
                  {formatDateNumber(date)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.navButtonPlaceholder} />
        </View>
      </View>

      {/* Badge Pills Section */}
      <View style={styles.badgesContainer}>
        {/* Attendance Badge */}
        <TouchableOpacity
          style={styles.badge}
          onPress={() => {
            router.push({
              pathname: "/attendance",
              params: {
                weekStart: weekStartDate.toISOString(),
                weekEnd: weekEndDate.toISOString(),
              },
            } as any);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="time-outline" size={24} color={Colors.white} />
          <View style={styles.badgeNumber}>
            <Text style={styles.badgeNumberText}>
              {attendanceLoading
                ? "..."
                : attendanceData?.myAttendances?.length || 0}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Shifts Badge */}
        <TouchableOpacity
          style={styles.badge}
          onPress={() => {
            router.push({
              pathname: "/shifts",
              params: {
                weekStart: weekStartDate.toISOString(),
                weekEnd: weekEndDate.toISOString(),
              },
            } as any);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="calendar-outline" size={24} color={Colors.white} />
          <View style={styles.badgeNumber}>
            <Text style={styles.badgeNumberText}>
              {shiftsLoading ? "..." : shiftsData?.getUserShifts?.length || 0}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Holidays Badge */}
        <TouchableOpacity
          style={styles.badge}
          onPress={() => {
            router.push({
              pathname: "/holidays",
              params: {
                weekStart: weekStartDate.toISOString(),
                weekEnd: weekEndDate.toISOString(),
              },
            } as any);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="sunny-outline" size={24} color={Colors.white} />
          <View style={styles.badgeNumber}>
            <Text style={styles.badgeNumberText}>
              {holidaysLoading ? "..." : weekHolidays?.length || 0}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Week Details Section */}
      <View style={styles.weekDetailsContainer}>
        {/* Attendance Details */}
        <View style={styles.detailSection}>
          <View style={styles.detailHeader}>
            <Ionicons name="time-outline" size={18} color={Colors.primary} />
            <Text style={styles.detailTitle}>Attendance This Week</Text>
          </View>
          {attendanceLoading ? (
            <ActivityIndicator
              size="small"
              color={Colors.primary}
              style={styles.detailLoader}
            />
          ) : attendanceData?.myAttendances &&
            attendanceData.myAttendances.length > 0 ? (
            <View style={styles.detailContent}>
              {attendanceData.myAttendances.map(
                (attendance: any, index: number) => (
                  <View key={attendance.id || index} style={styles.detailItem}>
                    <Text style={styles.detailItemDate}>
                      {new Date(attendance.date).toLocaleDateString("en-US", {
                        weekday: "short",
                        month: "short",
                        day: "numeric",
                      })}
                    </Text>
                    <Text style={styles.detailItemInfo}>
                      {attendance.timeSpentInMinutes
                        ? `${Math.floor(attendance.timeSpentInMinutes / 60)}h ${
                            attendance.timeSpentInMinutes % 60
                          }m`
                        : "Present"}
                    </Text>
                  </View>
                )
              )}
            </View>
          ) : (
            <Text style={styles.detailEmpty}>
              No attendance records this week
            </Text>
          )}
        </View>

        {/* Shifts Details */}
        <View style={styles.detailSection}>
          <View style={styles.detailHeader}>
            <Ionicons
              name="calendar-outline"
              size={18}
              color={Colors.primary}
            />
            <Text style={styles.detailTitle}>Shifts This Week</Text>
          </View>
          {shiftsLoading ? (
            <ActivityIndicator
              size="small"
              color={Colors.primary}
              style={styles.detailLoader}
            />
          ) : shiftsData?.getUserShifts &&
            shiftsData.getUserShifts.length > 0 ? (
            <View style={styles.detailContent}>
              {shiftsData.getUserShifts.map((shift: any, index: number) => (
                <View key={shift.id || index} style={styles.detailItem}>
                  <Text style={styles.detailItemDate}>
                    {new Date(shift.startDateTime).toLocaleDateString("en-US", {
                      weekday: "short",
                      month: "short",
                      day: "numeric",
                    })}
                  </Text>
                  <Text style={styles.detailItemInfo}>
                    {new Date(shift.startDateTime).toLocaleTimeString("en-US", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}{" "}
                    -{" "}
                    {new Date(shift.endDateTime).toLocaleTimeString("en-US", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </Text>
                  {shift.location?.name && (
                    <Text style={styles.detailItemLocation}>
                      {shift.location.name}
                    </Text>
                  )}
                </View>
              ))}
            </View>
          ) : (
            <Text style={styles.detailEmpty}>
              No shifts scheduled this week
            </Text>
          )}
        </View>

        {/* Holidays Details */}
        <View style={styles.detailSection}>
          <View style={styles.detailHeader}>
            <Ionicons name="sunny-outline" size={18} color={Colors.primary} />
            <Text style={styles.detailTitle}>Holidays This Week</Text>
          </View>
          {holidaysLoading ? (
            <ActivityIndicator
              size="small"
              color={Colors.primary}
              style={styles.detailLoader}
            />
          ) : weekHolidays?.length > 0 ? (
            <View style={styles.detailContent}>
              {weekHolidays.map((holiday: any, index: number) => (
                <View key={holiday.id || index} style={styles.detailItem}>
                  <Text style={styles.detailItemDate}>
                    {new Date(holiday.date).toLocaleDateString("en-US", {
                      weekday: "short",
                      month: "short",
                      day: "numeric",
                    })}
                  </Text>
                  <Text style={styles.detailItemInfo}>{holiday.name}</Text>
                  {holiday.description && (
                    <Text style={styles.detailItemLocation}>
                      {holiday.description}
                    </Text>
                  )}
                </View>
              ))}
            </View>
          ) : (
            <Text style={styles.detailEmpty}>No holidays this week</Text>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  monthHeader: {
    backgroundColor: Colors.white,
    paddingVertical: 10,
    paddingLeft: 20,
    paddingRight: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  monthText: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    textAlign: "left",
  },
  calendarContainer: {
    backgroundColor: Colors.white,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  weekContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 8,
    marginBottom: 4,
  },
  navButton: {
    padding: 4,
    width: 26,
    alignItems: "center",
  },
  weekDayText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
    width: 40,
  },
  datesContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 8,
  },
  navButtonPlaceholder: {
    width: 26,
  },
  datesRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    flex: 1,
  },
  dateCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedDateCircle: {
    backgroundColor: Colors.primary,
  },
  dateText: {
    fontSize: 16,
    color: Colors.text,
    fontWeight: "500",
  },
  selectedDateText: {
    color: Colors.white,
    fontWeight: "bold",
  },

  // Badge Pills styles
  badgesContainer: {
    flexDirection: "row",
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    gap: 8,
  },
  badge: {
    backgroundColor: Colors.primary,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    gap: 8,
  },

  badgeNumber: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 6,
  },
  badgeNumberText: {
    fontSize: 12,
    fontWeight: "bold",
    color: Colors.primary,
  },
  // Week Details styles
  weekDetailsContainer: {
    backgroundColor: Colors.background,
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  detailSection: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  detailHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 8,
  },
  detailTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  detailLoader: {
    alignSelf: "flex-start",
    marginTop: 8,
  },
  detailContent: {
    gap: 8,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: Colors.background,
    borderRadius: 8,
    gap: 12,
  },
  detailItemDate: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.primary,
    minWidth: 60,
  },
  detailItemInfo: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  detailItemLocation: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontStyle: "italic",
  },
  detailEmpty: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontStyle: "italic",
    textAlign: "center",
    paddingVertical: 12,
  },
});
