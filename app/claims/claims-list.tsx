import React, { useState, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { useFocusEffect } from "@react-navigation/native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { DateRangeFilter } from "@/components/filters/DateRangeFilter";
import {
  useClaimsQuery,
  ClaimType,
  ClaimStatus,
  Claims,
  FindClaimsInput,
} from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import { enumToOptions } from "@/lib/utils";
import { useRouter } from "expo-router";

/**
 * Filter Button Component
 */
interface FilterButtonProps {
  option: { value: string; label: string };
  isActive: boolean;
  onPress: (value: string) => void;
}

function FilterButton({ option, isActive, onPress }: FilterButtonProps) {
  return (
    <TouchableOpacity
      style={[styles.filterButton, isActive && styles.activeFilterButton]}
      onPress={() => onPress(option.value)}
    >
      <Text
        style={[
          styles.filterButtonText,
          isActive && styles.activeFilterButtonText,
        ]}
      >
        {option.label}
      </Text>
    </TouchableOpacity>
  );
}

/**
 * Claim Item Component
 */
interface ClaimItemProps {
  claim: Claims;
  onPress: (claim: Claims) => void;
}

function ClaimItem({ claim, onPress }: ClaimItemProps) {
  const getStatusColor = (status?: ClaimStatus | null) => {
    switch (status) {
      case ClaimStatus.Approved:
        return Colors.success;
      case ClaimStatus.Rejected:
        return Colors.error;
      case ClaimStatus.Pending:
      default:
        return Colors.warning;
    }
  };

  const getStatusIcon = (status?: ClaimStatus | null) => {
    switch (status) {
      case ClaimStatus.Approved:
        return "checkmark-circle";
      case ClaimStatus.Rejected:
        return "close-circle";
      case ClaimStatus.Pending:
      default:
        return "time";
    }
  };

  const getClaimTypeIcon = (type: ClaimType) => {
    switch (type) {
      case ClaimType.Allowance:
        return "time-outline";
      case ClaimType.Travel:
        return "car-outline";
      case ClaimType.Expense:
        return "receipt-outline";
      case ClaimType.Site:
        return "location-outline";
      default:
        return "document-outline";
    }
  };

  const formatAmount = (amount?: number | null) => {
    if (!amount) return "N/A";
    return `RM ${amount.toFixed(2)}`;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getClaimAmount = () => {
    const claimData = claim.claimData;
    if ("amount" in claimData) {
      return claimData.amount;
    }
    return null;
  };

  return (
    <TouchableOpacity
      style={styles.claimItem}
      onPress={() => onPress(claim)}
      activeOpacity={0.7}
    >
      <View style={styles.claimHeader}>
        <View style={styles.claimTypeContainer}>
          <Ionicons
            name={getClaimTypeIcon(claim.claimType)}
            size={20}
            color={Colors.primary}
          />
          <Text style={styles.claimType}>{claim.claimType}</Text>
        </View>
        <View style={styles.statusContainer}>
          <Ionicons
            name={getStatusIcon(claim.status)}
            size={16}
            color={getStatusColor(claim.status)}
          />
          <Text
            style={[styles.status, { color: getStatusColor(claim.status) }]}
          >
            {claim.status || "PENDING"}
          </Text>
        </View>
      </View>

      <View style={styles.claimContent}>
        <Text style={styles.amount}>{formatAmount(getClaimAmount())}</Text>
        <Text style={styles.date}>
          Submitted: {formatDate(claim.createdAt)}
        </Text>
        {claim.rejectedReason && (
          <Text style={styles.rejectedReason}>
            Reason: {claim.rejectedReason}
          </Text>
        )}
      </View>

      <View style={styles.claimFooter}>
        <Text style={styles.submittedBy}>
          By: {claim.user.fullname} ({claim.user.employeeId})
        </Text>
        {claim.processedBy && (
          <Text style={styles.processedBy}>
            Processed by: {claim.processedBy.fullname}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

export default function ClaimsList() {
  const router = useRouter();
  const { session } = useSession();
  const [claimType, setClaimType] = useState<ClaimType | null>(null);
  const [status, setStatus] = useState<ClaimStatus | null>(null);
  const [fromDate, setFromDate] = useState<string | null>(null);
  const [toDate, setToDate] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Build filter input
  const filterInput: FindClaimsInput = {
    user: session?.userId || undefined,
    claimType: claimType || undefined,
    status: status || undefined,
    from: fromDate ? new Date(fromDate) : undefined,
    to: toDate ? new Date(toDate) : undefined,
  };

  // Fetch claims using GraphQL query
  const {
    data: claimsData,
    isLoading,
    error,
    refetch,
  } = useClaimsQuery(
    { input: filterInput },
    {
      enabled: !!session?.userId,
      initialData: { claims: [] },
    }
  );

  const claims = claimsData?.claims || [];

  // Handle date range change
  const handleDateRangeChange = useCallback(
    (from: string | null, to: string | null) => {
      setFromDate(from);
      setToDate(to);
    },
    []
  );

  // Handle claim item press
  const handleClaimPress = (claim: Claims) => {
    router.push(`/claims/${claim._id}`);
  };

  // Handle pull to refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  }, [refetch]);

  // Refetch data when filter parameters change
  React.useEffect(() => {
    if (session?.userId) {
      refetch();
    }
  }, [claimType, status, fromDate, toDate, session?.userId, refetch]);

  // Refetch data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (session?.userId) {
        refetch();
      }
    }, [session?.userId, refetch])
  );

  // Clear all filters
  const clearFilters = () => {
    setClaimType(null);
    setStatus(null);
    setFromDate(null);
    setToDate(null);
  };

  const renderClaimItem = ({ item }: { item: Claims }) => (
    <ClaimItem claim={item} onPress={handleClaimPress} />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-outline" size={64} color={Colors.textLight} />
      <Text style={styles.emptyStateTitle}>No Claims Found</Text>
      <Text style={styles.emptyStateText}>
        {claimType || status || fromDate || toDate
          ? "Try adjusting your filters to see more results."
          : "You haven't submitted any claims yet."}
      </Text>
      {(claimType || status || fromDate || toDate) && (
        <TouchableOpacity
          style={styles.clearFiltersButton}
          onPress={clearFilters}
        >
          <Text style={styles.clearFiltersText}>Clear Filters</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={48} color={Colors.error} />
        <Text style={styles.errorText}>Failed to load claims</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      {/* Filter Section */}
      <View style={styles.filterContainer}>
        {/* Date Range Filter */}
        <Text style={styles.filterLabel}>Date Range:</Text>
        <DateRangeFilter
          fromDate={fromDate}
          toDate={toDate}
          onDateRangeChange={handleDateRangeChange}
          containerStyle={styles.dateRangeFilter}
        />

        {/* Claim Type Filter */}
        <Text style={styles.filterLabel}>Claim Type:</Text>
        <FlatList
          data={[
            { value: "", label: "All" },
            ...enumToOptions(ClaimType).filter((item) => item.value !== "SITE"),
          ]}
          keyExtractor={(item) => item.value}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={claimType === (item.value || null)}
              onPress={(value) =>
                setClaimType(value ? (value as ClaimType) : null)
              }
            />
          )}
        />

        {/* Status Filter */}
        <Text style={styles.filterLabel}>Status:</Text>
        <FlatList
          data={[{ value: "", label: "All" }, ...enumToOptions(ClaimStatus)]}
          keyExtractor={(item) => item.value}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={status === (item.value || null)}
              onPress={(value) =>
                setStatus(value ? (value as ClaimStatus) : null)
              }
            />
          )}
        />
      </View>

      {/* Claims List */}
      {isLoading && !isRefreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading claims...</Text>
        </View>
      ) : (
        <FlatList
          //@ts-ignore
          data={claims}
          keyExtractor={(item) => item._id}
          renderItem={renderClaimItem}
          contentContainerStyle={styles.claimsList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  filterContainer: {
    backgroundColor: Colors.white,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 8,
    marginTop: 12,
  },
  dateRangeFilter: {
    marginBottom: 8,
  },
  filterList: {
    paddingBottom: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: Colors.lightGray,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  activeFilterButton: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: "500",
  },
  activeFilterButtonText: {
    color: Colors.white,
    fontWeight: "600",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  claimsList: {
    padding: 16,
    flexGrow: 1,
  },
  claimItem: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  claimHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  claimTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  claimType: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginLeft: 8,
    textTransform: "capitalize",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.lightGray,
  },
  status: {
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
    textTransform: "capitalize",
  },
  claimContent: {
    marginBottom: 12,
  },
  amount: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.primary,
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  rejectedReason: {
    fontSize: 14,
    color: Colors.error,
    fontStyle: "italic",
    marginTop: 4,
  },
  claimFooter: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 8,
  },
  submittedBy: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  processedBy: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 16,
  },
  clearFiltersButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  clearFiltersText: {
    color: Colors.white,
    fontSize: 14,
    fontWeight: "600",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    marginTop: 16,
    marginBottom: 16,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: Colors.white,
    fontSize: 14,
    fontWeight: "600",
  },
});
