import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { useInventoryRequestDetailsQuery } from "@/generated/graphql";
import { format } from "date-fns";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
} from "react-native-reanimated";

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = () => {
    switch (status.toLowerCase()) {
      case "pending":
        return Colors.warning;
      case "approved":
        return Colors.success;
      case "rejected":
        return Colors.error;
      default:
        return Colors.textSecondary;
    }
  };

  return (
    <View style={[styles.statusBadge, { backgroundColor: getStatusColor() }]}>
      <Text style={styles.statusText}>{status}</Text>
    </View>
  );
};

// Info row component
const InfoRow = ({ 
  icon, 
  label, 
  value, 
  iconColor = Colors.textSecondary 
}: {
  icon: string;
  label: string;
  value: string;
  iconColor?: string;
}) => (
  <View style={styles.infoRow}>
    <View style={styles.infoIcon}>
      <Ionicons name={icon as any} size={20} color={iconColor} />
    </View>
    <View style={styles.infoContent}>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={styles.infoValue}>{value}</Text>
    </View>
  </View>
);

// Item card component
const ItemCard = ({ item, index }: any) => {
  const cardOpacity = useSharedValue(0);
  const cardTranslateY = useSharedValue(20);

  useEffect(() => {
    cardOpacity.value = withDelay(index * 100, withTiming(1, { duration: 300 }));
    cardTranslateY.value = withDelay(index * 100, withTiming(0, { duration: 300 }));
  }, [index]);

  const cardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ translateY: cardTranslateY.value }],
  }));

  return (
    <Animated.View style={cardAnimatedStyle}>
      <View style={styles.itemCard}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemName}>{item.item}</Text>
          <View style={styles.quantityBadge}>
            <Text style={styles.quantityText}>Qty: {item.quantity}</Text>
          </View>
        </View>
        
        <View style={styles.itemDetails}>
          <Text style={styles.itemSku}>SKU: {item.sku}</Text>
          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>Cost: ${item.costPrice}</Text>
            <Text style={styles.priceLabel}>Selling: ${item.sellingPrice}</Text>
          </View>
        </View>

        {item.selectedAttributes && item.selectedAttributes.length > 0 && (
          <View style={styles.attributesContainer}>
            <Text style={styles.attributesTitle}>Attributes:</Text>
            {item.selectedAttributes.map((attr: any, idx: number) => (
              <View key={idx} style={styles.attributeRow}>
                <Text style={styles.attributeName}>{attr.attributeName}:</Text>
                <Text style={styles.attributeValue}>{attr.value}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </Animated.View>
  );
};

export default function InventoryDetailsScreen() {
  const router = useRouter();
  const { requestId } = useLocalSearchParams<{ requestId: string }>();

  // Animation values
  const headerOpacity = useSharedValue(0);
  const contentOpacity = useSharedValue(0);

  // Fetch inventory request details
  const { data, isLoading, error } = useInventoryRequestDetailsQuery(
    {
      id: requestId!,
    },
    {
      enabled: !!requestId,
    }
  );

  useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 500 });
    contentOpacity.value = withDelay(200, withTiming(1, { duration: 500 }));
  }, []);

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value,
  }));

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <StatusBar style="light" />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
            <Ionicons name="chevron-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Request Details</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading request details...</Text>
        </View>
      </View>
    );
  }

  if (error || !data?.inventoryRequest) {
    return (
      <View style={styles.container}>
        <StatusBar style="light" />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
            <Ionicons name="chevron-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Request Details</Text>
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={Colors.error} />
          <Text style={styles.errorTitle}>Request Not Found</Text>
          <Text style={styles.errorSubtitle}>
            The inventory request could not be loaded.
          </Text>
        </View>
      </View>
    );
  }

  const request = data.inventoryRequest;

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Header */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Request Details</Text>
      </Animated.View>

      {/* Content */}
      <Animated.View style={[styles.content, contentAnimatedStyle]}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Request Overview */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Request Overview</Text>
              <StatusBadge status={request.status} />
            </View>
            
            <View style={styles.overviewCard}>
              <InfoRow
                icon="cube-outline"
                label="Inventory Item"
                value={request.inventory.item}
              />
              <InfoRow
                icon="person-outline"
                label="Requested By"
                value={request.requestedBy.fullname}
              />
              <InfoRow
                icon="calendar-outline"
                label="Request Date"
                value={format(new Date(request.createdAt), "MMMM dd, yyyy 'at' HH:mm")}
              />
              {request.acceptedAt && (
                <InfoRow
                  icon="checkmark-circle-outline"
                  label="Accepted Date"
                  value={format(new Date(request.acceptedAt), "MMMM dd, yyyy 'at' HH:mm")}
                  iconColor={Colors.success}
                />
              )}
              <InfoRow
                icon="pricetag-outline"
                label="Inventory Type"
                value={request.inventoryType}
              />
            </View>
          </View>

          {/* Requested Items */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Requested Items ({request.items.length})
            </Text>
            {request.items.map((item, index) => (
              <ItemCard key={index} item={item} index={index} />
            ))}
          </View>
        </ScrollView>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: Colors.white,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: Colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.white,
    textTransform: "uppercase",
  },
  overviewCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: Colors.text,
    fontWeight: "500",
  },
  itemCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    flex: 1,
  },
  quantityBadge: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  quantityText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.white,
  },
  itemDetails: {
    marginBottom: 12,
  },
  itemSku: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  priceLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  attributesContainer: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 12,
  },
  attributesTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 8,
  },
  attributeRow: {
    flexDirection: "row",
    marginBottom: 4,
  },
  attributeName: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginRight: 8,
    fontWeight: "500",
  },
  attributeValue: {
    fontSize: 14,
    color: Colors.text,
  },
});
